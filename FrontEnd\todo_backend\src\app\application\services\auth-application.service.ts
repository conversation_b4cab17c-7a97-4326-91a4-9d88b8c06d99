import { Injectable } from '@angular/core';
import { Observable, tap, finalize } from 'rxjs';
import { Router } from '@angular/router';
import { AuthRepository } from '../../infrastructure/repositories/auth.repository';
import { AuthService } from '../../core/services/auth.service';
import { LoadingService } from '../../core/services/loading.service';
import { ErrorHandlingService } from '../../core/services/error-handling.service';
import { AuthResponse, CreateUser, LoginCredentials, User } from '../../domain/models/user.model';

/**
 * Application service for authentication business logic
 * Orchestrates authentication operations and manages application state
 */
@Injectable({
  providedIn: 'root'
})
export class AuthApplicationService {
  
  constructor(
    private authRepository: AuthRepository,
    private authService: AuthService,
    private loadingService: LoadingService,
    private errorHandlingService: ErrorHandlingService,
    private router: Router
  ) {}

  /**
   * Register a new user
   */
  register(userData: CreateUser): Observable<AuthResponse> {
    this.loadingService.show();
    this.errorHandlingService.clearError();

    return this.authRepository.register(userData).pipe(
      tap(response => {
        this.authService.setAuthData(response);
        this.router.navigate(['/todos']);
      }),
      finalize(() => this.loadingService.hide())
    );
  }

  /**
   * Login user
   */
  login(credentials: LoginCredentials): Observable<AuthResponse> {
    this.loadingService.show();
    this.errorHandlingService.clearError();

    return this.authRepository.login(credentials).pipe(
      tap(response => {
        this.authService.setAuthData(response);
        this.router.navigate(['/todos']);
      }),
      finalize(() => this.loadingService.hide())
    );
  }

  /**
   * Logout user
   */
  logout(): Observable<void> {
    this.loadingService.show();

    return this.authRepository.logout().pipe(
      tap(() => {
        this.authService.clearAuthData();
        this.router.navigate(['/login']);
      }),
      finalize(() => this.loadingService.hide())
    );
  }

  /**
   * Get current user
   */
  getCurrentUser(): User | null {
    return this.authService.getCurrentUser();
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return this.authService.isAuthenticated();
  }

  /**
   * Get authentication state observable
   */
  getAuthState(): Observable<any> {
    return this.authService.authState$;
  }

  /**
   * Verify token validity
   */
  verifyToken(): Observable<boolean> {
    return this.authRepository.verifyToken();
  }
}
