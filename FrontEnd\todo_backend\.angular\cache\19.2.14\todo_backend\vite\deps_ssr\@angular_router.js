import { createRequire } from 'module';const require = createRequire(import.meta.url);
import {
  ActivatedRoute,
  ActivatedRouteSnapshot,
  ActivationEnd,
  ActivationStart,
  BaseRouteReuseStrategy,
  ChildActivationEnd,
  ChildActivationStart,
  ChildrenOutletContexts,
  DefaultTitleStrategy,
  DefaultUrlSerializer,
  EventType,
  GuardsCheckEnd,
  GuardsCheckStart,
  NavigationCancel,
  NavigationCancellationCode,
  NavigationEnd,
  NavigationError,
  NavigationSkipped,
  NavigationSkippedCode,
  NavigationStart,
  NoPreloading,
  OutletContext,
  PRIMARY_OUTLET,
  PreloadAllModules,
  PreloadingStrategy,
  ROUTER_CONFIGURATION,
  ROUTER_INITIALIZER,
  ROUTER_OUTLET_DATA,
  ROUTER_PROVIDERS,
  ROUTES,
  RedirectCommand,
  ResolveEnd,
  ResolveStart,
  RouteConfigLoadEnd,
  RouteConfigLoadStart,
  RouteReuseStrategy,
  Router,
  RouterEvent,
  RouterLink,
  Router<PERSON>inkActive,
  RouterModule,
  RouterOutlet,
  RouterPreloader,
  RouterState,
  RouterStateSnapshot,
  RoutesRecognized,
  Scroll,
  TitleStrategy,
  UrlHandlingStrategy,
  UrlSegment,
  UrlSegmentGroup,
  UrlSerializer,
  UrlTree,
  VERSION,
  afterNextNavigation,
  convertToParamMap,
  createUrlTreeFromSnapshot,
  defaultUrlMatcher,
  loadChildren,
  mapToCanActivate,
  mapToCanActivateChild,
  mapToCanDeactivate,
  mapToCanMatch,
  mapToResolve,
  provideRouter,
  provideRoutes,
  withComponentInputBinding,
  withDebugTracing,
  withDisabledInitialNavigation,
  withEnabledBlockingInitialNavigation,
  withHashLocation,
  withInMemoryScrolling,
  withNavigationErrorHandler,
  withPreloading,
  withRouterConfig,
  withViewTransitions,
  ɵEmptyOutletComponent
} from "./chunk-LD4LD2AQ.js";
import "./chunk-SXOHPYOW.js";
import "./chunk-MXL36PNX.js";
import "./chunk-AVCKQTS2.js";
import "./chunk-SCA5T6B5.js";
import "./chunk-LRGD6IXK.js";
import "./chunk-XCIYP5SE.js";
import "./chunk-ZUJ64LXG.js";
import "./chunk-OYTRG5F6.js";
import "./chunk-YHCV7DAQ.js";
export {
  ActivatedRoute,
  ActivatedRouteSnapshot,
  ActivationEnd,
  ActivationStart,
  BaseRouteReuseStrategy,
  ChildActivationEnd,
  ChildActivationStart,
  ChildrenOutletContexts,
  DefaultTitleStrategy,
  DefaultUrlSerializer,
  EventType,
  GuardsCheckEnd,
  GuardsCheckStart,
  NavigationCancel,
  NavigationCancellationCode,
  NavigationEnd,
  NavigationError,
  NavigationSkipped,
  NavigationSkippedCode,
  NavigationStart,
  NoPreloading,
  OutletContext,
  PRIMARY_OUTLET,
  PreloadAllModules,
  PreloadingStrategy,
  ROUTER_CONFIGURATION,
  ROUTER_INITIALIZER,
  ROUTER_OUTLET_DATA,
  ROUTES,
  RedirectCommand,
  ResolveEnd,
  ResolveStart,
  RouteConfigLoadEnd,
  RouteConfigLoadStart,
  RouteReuseStrategy,
  Router,
  RouterEvent,
  RouterLink,
  RouterLinkActive,
  RouterLink as RouterLinkWithHref,
  RouterModule,
  RouterOutlet,
  RouterPreloader,
  RouterState,
  RouterStateSnapshot,
  RoutesRecognized,
  Scroll,
  TitleStrategy,
  UrlHandlingStrategy,
  UrlSegment,
  UrlSegmentGroup,
  UrlSerializer,
  UrlTree,
  VERSION,
  convertToParamMap,
  createUrlTreeFromSnapshot,
  defaultUrlMatcher,
  mapToCanActivate,
  mapToCanActivateChild,
  mapToCanDeactivate,
  mapToCanMatch,
  mapToResolve,
  provideRouter,
  provideRoutes,
  withComponentInputBinding,
  withDebugTracing,
  withDisabledInitialNavigation,
  withEnabledBlockingInitialNavigation,
  withHashLocation,
  withInMemoryScrolling,
  withNavigationErrorHandler,
  withPreloading,
  withRouterConfig,
  withViewTransitions,
  ɵEmptyOutletComponent,
  ROUTER_PROVIDERS as ɵROUTER_PROVIDERS,
  afterNextNavigation as ɵafterNextNavigation,
  loadChildren as ɵloadChildren
};
