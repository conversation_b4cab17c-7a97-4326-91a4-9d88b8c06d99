/**
 * Domain models for Todo entities
 * These interfaces define the structure of data used throughout the application
 */

export interface TodoItem {
  id: string;
  title: string;
  description?: string;
  isCompleted: boolean;
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
  userId: string;
}

export interface CreateTodoItem {
  title: string;
  description?: string;
}

export interface UpdateTodoItem {
  title: string;
  description?: string;
  isCompleted: boolean;
}

export interface TodoFilter {
  isCompleted?: boolean;
  createdAfter?: Date;
  createdBefore?: Date;
  searchTerm?: string;
}

export interface TodoStats {
  totalCount: number;
  completedCount: number;
  pendingCount: number;
}
