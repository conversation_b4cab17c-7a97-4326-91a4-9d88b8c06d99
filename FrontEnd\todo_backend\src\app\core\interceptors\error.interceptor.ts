import { HttpInterceptorFn } from '@angular/common/http';
import { inject } from '@angular/core';
import { catchError, throwError } from 'rxjs';
import { Router } from '@angular/router';
import { ErrorHandlingService } from '../services/error-handling.service';

/**
 * HTTP Interceptor for global error handling
 */
export const errorInterceptor: HttpInterceptorFn = (req, next) => {
  const router = inject(Router);
  const errorHandlingService = inject(ErrorHandlingService);

  return next(req).pipe(
    catchError((error) => {
      // Handle specific HTTP status codes
      switch (error.status) {
        case 401:
          // Unauthorized - redirect to login
          localStorage.removeItem('auth_token');
          router.navigate(['/login']);
          break;
        case 403:
          // Forbidden
          errorHandlingService.handleError('Access denied. You do not have permission to perform this action.');
          break;
        case 404:
          // Not found
          errorHandlingService.handleError('The requested resource was not found.');
          break;
        case 500:
          // Server error
          errorHandlingService.handleError('Internal server error. Please try again later.');
          break;
        default:
          // Other errors
          errorHandlingService.handleError(error.error?.message || 'An unexpected error occurred.');
      }

      return throwError(() => error);
    })
  );
};
