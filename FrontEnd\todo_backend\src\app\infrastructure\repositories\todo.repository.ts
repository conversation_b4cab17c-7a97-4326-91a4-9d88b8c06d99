import { Injectable } from '@angular/core';
import { Observable, map } from 'rxjs';
import { HttpParams } from '@angular/common/http';
import { ITodoRepository } from '../../domain/repositories/todo.repository.interface';
import { TodoItem, CreateTodoItem, UpdateTodoItem, TodoFilter } from '../../domain/models/todo.model';
import { HttpClientService } from '../../core/services/http-client.service';

/**
 * Implementation of todo repository
 * Handles HTTP communication for todo operations
 */
@Injectable({
  providedIn: 'root'
})
export class TodoRepository implements ITodoRepository {
  
  constructor(private httpClient: HttpClientService) {}

  /**
   * Get all todos for the authenticated user
   */
  getAllTodos(): Observable<TodoItem[]> {
    return this.httpClient.get<TodoItem[]>('todos')
      .pipe(
        map(todos => todos.map(this.mapTodoItem))
      );
  }

  /**
   * Get filtered todos based on criteria
   */
  getFilteredTodos(filter: TodoFilter): Observable<TodoItem[]> {
    let params = new HttpParams();
    
    if (filter.isCompleted !== undefined) {
      params = params.set('completed', filter.isCompleted.toString());
    }
    
    if (filter.searchTerm) {
      params = params.set('search', filter.searchTerm);
    }
    
    if (filter.createdAfter) {
      params = params.set('createdAfter', filter.createdAfter.toISOString());
    }
    
    if (filter.createdBefore) {
      params = params.set('createdBefore', filter.createdBefore.toISOString());
    }

    return this.httpClient.get<TodoItem[]>('todos/filter', params)
      .pipe(
        map(todos => todos.map(this.mapTodoItem))
      );
  }

  /**
   * Get a specific todo by ID
   */
  getTodoById(id: string): Observable<TodoItem> {
    return this.httpClient.get<TodoItem>(`todos/${id}`)
      .pipe(
        map(this.mapTodoItem)
      );
  }

  /**
   * Create a new todo
   */
  createTodo(todo: CreateTodoItem): Observable<TodoItem> {
    return this.httpClient.post<TodoItem>('todos', todo)
      .pipe(
        map(this.mapTodoItem)
      );
  }

  /**
   * Update an existing todo
   */
  updateTodo(id: string, todo: UpdateTodoItem): Observable<TodoItem> {
    return this.httpClient.put<TodoItem>(`todos/${id}`, todo)
      .pipe(
        map(this.mapTodoItem)
      );
  }

  /**
   * Delete a todo
   */
  deleteTodo(id: string): Observable<void> {
    return this.httpClient.delete<void>(`todos/${id}`);
  }

  /**
   * Get todo statistics
   */
  getTodoStats(): Observable<{ totalCount: number; completedCount: number }> {
    return this.httpClient.get<{ totalCount: number; completedCount: number }>('todos/stats');
  }

  /**
   * Map API response to TodoItem model
   */
  private mapTodoItem = (item: any): TodoItem => ({
    id: item.id,
    title: item.title,
    description: item.description,
    isCompleted: item.isCompleted,
    createdAt: new Date(item.createdAt),
    updatedAt: new Date(item.updatedAt),
    completedAt: item.completedAt ? new Date(item.completedAt) : undefined,
    userId: item.userId
  });
}
