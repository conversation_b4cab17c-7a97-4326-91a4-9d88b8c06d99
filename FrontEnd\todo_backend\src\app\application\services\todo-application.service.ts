import { Injectable } from '@angular/core';
import { Observable, BehaviorSubject, tap, finalize, map } from 'rxjs';
import { TodoRepository } from '../../infrastructure/repositories/todo.repository';
import { LoadingService } from '../../core/services/loading.service';
import { ErrorHandlingService } from '../../core/services/error-handling.service';
import { TodoItem, CreateTodoItem, UpdateTodoItem, TodoFilter, TodoStats } from '../../domain/models/todo.model';

/**
 * Application service for todo business logic
 * Manages todo state and orchestrates todo operations
 */
@Injectable({
  providedIn: 'root'
})
export class TodoApplicationService {
  private todosSubject = new BehaviorSubject<TodoItem[]>([]);
  private statsSubject = new BehaviorSubject<TodoStats>({
    totalCount: 0,
    completedCount: 0,
    pendingCount: 0
  });

  public todos$ = this.todosSubject.asObservable();
  public stats$ = this.statsSubject.asObservable();

  constructor(
    private todoRepository: TodoRepository,
    private loadingService: LoadingService,
    private errorHandlingService: ErrorHandlingService
  ) {}

  /**
   * Load all todos
   */
  loadTodos(): Observable<TodoItem[]> {
    this.loadingService.show();
    this.errorHandlingService.clearError();

    return this.todoRepository.getAllTodos().pipe(
      tap(todos => {
        this.todosSubject.next(todos);
        this.updateStats(todos);
      }),
      finalize(() => this.loadingService.hide())
    );
  }

  /**
   * Load filtered todos
   */
  loadFilteredTodos(filter: TodoFilter): Observable<TodoItem[]> {
    this.loadingService.show();
    this.errorHandlingService.clearError();

    return this.todoRepository.getFilteredTodos(filter).pipe(
      tap(todos => {
        this.todosSubject.next(todos);
        this.updateStats(todos);
      }),
      finalize(() => this.loadingService.hide())
    );
  }

  /**
   * Get todo by ID
   */
  getTodoById(id: string): Observable<TodoItem> {
    this.loadingService.show();

    return this.todoRepository.getTodoById(id).pipe(
      finalize(() => this.loadingService.hide())
    );
  }

  /**
   * Create a new todo
   */
  createTodo(todo: CreateTodoItem): Observable<TodoItem> {
    this.loadingService.show();
    this.errorHandlingService.clearError();

    return this.todoRepository.createTodo(todo).pipe(
      tap(newTodo => {
        const currentTodos = this.todosSubject.value;
        const updatedTodos = [newTodo, ...currentTodos];
        this.todosSubject.next(updatedTodos);
        this.updateStats(updatedTodos);
      }),
      finalize(() => this.loadingService.hide())
    );
  }

  /**
   * Update an existing todo
   */
  updateTodo(id: string, todo: UpdateTodoItem): Observable<TodoItem> {
    this.loadingService.show();
    this.errorHandlingService.clearError();

    return this.todoRepository.updateTodo(id, todo).pipe(
      tap(updatedTodo => {
        const currentTodos = this.todosSubject.value;
        const updatedTodos = currentTodos.map(t => 
          t.id === id ? updatedTodo : t
        );
        this.todosSubject.next(updatedTodos);
        this.updateStats(updatedTodos);
      }),
      finalize(() => this.loadingService.hide())
    );
  }

  /**
   * Delete a todo
   */
  deleteTodo(id: string): Observable<void> {
    this.loadingService.show();
    this.errorHandlingService.clearError();

    return this.todoRepository.deleteTodo(id).pipe(
      tap(() => {
        const currentTodos = this.todosSubject.value;
        const updatedTodos = currentTodos.filter(t => t.id !== id);
        this.todosSubject.next(updatedTodos);
        this.updateStats(updatedTodos);
      }),
      finalize(() => this.loadingService.hide())
    );
  }

  /**
   * Toggle todo completion status
   */
  toggleTodoCompletion(id: string): Observable<TodoItem> {
    const todo = this.todosSubject.value.find(t => t.id === id);
    if (!todo) {
      throw new Error('Todo not found');
    }

    const updateData: UpdateTodoItem = {
      title: todo.title,
      description: todo.description,
      isCompleted: !todo.isCompleted
    };

    return this.updateTodo(id, updateData);
  }

  /**
   * Get current todos
   */
  getCurrentTodos(): TodoItem[] {
    return this.todosSubject.value;
  }

  /**
   * Get current stats
   */
  getCurrentStats(): TodoStats {
    return this.statsSubject.value;
  }

  /**
   * Update statistics based on current todos
   */
  private updateStats(todos: TodoItem[]): void {
    const totalCount = todos.length;
    const completedCount = todos.filter(t => t.isCompleted).length;
    const pendingCount = totalCount - completedCount;

    this.statsSubject.next({
      totalCount,
      completedCount,
      pendingCount
    });
  }
}
