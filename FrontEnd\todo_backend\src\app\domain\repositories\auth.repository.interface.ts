import { Observable } from 'rxjs';
import { AuthResponse, CreateUser, LoginCredentials } from '../models/user.model';

/**
 * Repository interface for authentication data access
 * This defines the contract for authentication operations
 */
export interface IAuthRepository {
  /**
   * Register a new user
   */
  register(userData: CreateUser): Observable<AuthResponse>;

  /**
   * Login with email and password
   */
  login(credentials: LoginCredentials): Observable<AuthResponse>;

  /**
   * Logout the current user
   */
  logout(): Observable<void>;

  /**
   * Refresh the authentication token
   */
  refreshToken(): Observable<AuthResponse>;

  /**
   * Verify if the current token is valid
   */
  verifyToken(): Observable<boolean>;
}
