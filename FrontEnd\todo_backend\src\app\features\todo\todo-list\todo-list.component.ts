import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';
import { TodoApplicationService } from '../../../application/services/todo-application.service';
import { LoadingService } from '../../../core/services/loading.service';
import { ErrorHandlingService } from '../../../core/services/error-handling.service';
import { TodoItem, CreateTodoItem, TodoFilter } from '../../../domain/models/todo.model';

/**
 * Todo list component - Presentation layer
 * Displays todos and handles user interactions
 */
@Component({
  selector: 'app-todo-list',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="todo-container">
      <h1>Todo List</h1>
      
      <!-- Error Display -->
      <div *ngIf="errorMessage" class="error-message">
        {{ errorMessage }}
        <button (click)="clearError()" class="close-btn">&times;</button>
      </div>

      <!-- Loading Indicator -->
      <div *ngIf="isLoading" class="loading">
        Loading...
      </div>

      <!-- Add Todo Form -->
      <div class="add-todo-form">
        <input 
          [(ngModel)]="newTodoTitle" 
          placeholder="Enter todo title"
          (keyup.enter)="addTodo()"
          class="todo-input">
        <textarea 
          [(ngModel)]="newTodoDescription" 
          placeholder="Enter description (optional)"
          class="todo-textarea"></textarea>
        <button (click)="addTodo()" [disabled]="!newTodoTitle.trim()" class="add-btn">
          Add Todo
        </button>
      </div>

      <!-- Filter Controls -->
      <div class="filter-controls">
        <button (click)="loadAllTodos()" class="filter-btn">All</button>
        <button (click)="loadCompletedTodos()" class="filter-btn">Completed</button>
        <button (click)="loadPendingTodos()" class="filter-btn">Pending</button>
        <input 
          [(ngModel)]="searchTerm" 
          (input)="onSearchChange()"
          placeholder="Search todos..."
          class="search-input">
      </div>

      <!-- Stats -->
      <div class="stats" *ngIf="stats">
        <span>Total: {{ stats.totalCount }}</span>
        <span>Completed: {{ stats.completedCount }}</span>
        <span>Pending: {{ stats.pendingCount }}</span>
      </div>

      <!-- Todo List -->
      <div class="todo-list">
        <div *ngFor="let todo of todos" class="todo-item" [class.completed]="todo.isCompleted">
          <div class="todo-content">
            <h3>{{ todo.title }}</h3>
            <p *ngIf="todo.description">{{ todo.description }}</p>
            <small>Created: {{ todo.createdAt | date:'short' }}</small>
          </div>
          <div class="todo-actions">
            <button (click)="toggleCompletion(todo.id)" class="toggle-btn">
              {{ todo.isCompleted ? 'Mark Pending' : 'Mark Complete' }}
            </button>
            <button (click)="deleteTodo(todo.id)" class="delete-btn">Delete</button>
          </div>
        </div>
      </div>

      <div *ngIf="todos.length === 0 && !isLoading" class="no-todos">
        No todos found. Add your first todo above!
      </div>
    </div>
  `,
  styles: [`
    .todo-container {
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }

    .error-message {
      background-color: #f8d7da;
      color: #721c24;
      padding: 10px;
      border-radius: 4px;
      margin-bottom: 20px;
      position: relative;
    }

    .close-btn {
      position: absolute;
      right: 10px;
      top: 5px;
      background: none;
      border: none;
      font-size: 20px;
      cursor: pointer;
    }

    .loading {
      text-align: center;
      padding: 20px;
      font-style: italic;
    }

    .add-todo-form {
      margin-bottom: 20px;
      padding: 20px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }

    .todo-input, .todo-textarea {
      width: 100%;
      padding: 10px;
      margin-bottom: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }

    .todo-textarea {
      height: 80px;
      resize: vertical;
    }

    .add-btn {
      background-color: #007bff;
      color: white;
      padding: 10px 20px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }

    .add-btn:disabled {
      background-color: #ccc;
      cursor: not-allowed;
    }

    .filter-controls {
      margin-bottom: 20px;
      display: flex;
      gap: 10px;
      align-items: center;
    }

    .filter-btn {
      padding: 8px 16px;
      border: 1px solid #ddd;
      background: white;
      border-radius: 4px;
      cursor: pointer;
    }

    .search-input {
      flex: 1;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }

    .stats {
      margin-bottom: 20px;
      display: flex;
      gap: 20px;
      font-weight: bold;
    }

    .todo-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 4px;
      margin-bottom: 10px;
    }

    .todo-item.completed {
      background-color: #f8f9fa;
      opacity: 0.7;
    }

    .todo-item.completed .todo-content h3 {
      text-decoration: line-through;
    }

    .todo-actions {
      display: flex;
      gap: 10px;
    }

    .toggle-btn {
      background-color: #28a745;
      color: white;
      padding: 5px 10px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }

    .delete-btn {
      background-color: #dc3545;
      color: white;
      padding: 5px 10px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }

    .no-todos {
      text-align: center;
      padding: 40px;
      color: #666;
      font-style: italic;
    }
  `]
})
export class TodoListComponent implements OnInit, OnDestroy {
  todos: TodoItem[] = [];
  stats: any = null;
  isLoading = false;
  errorMessage: string | null = null;
  
  newTodoTitle = '';
  newTodoDescription = '';
  searchTerm = '';
  
  private destroy$ = new Subject<void>();

  constructor(
    private todoService: TodoApplicationService,
    private loadingService: LoadingService,
    private errorHandlingService: ErrorHandlingService
  ) {}

  ngOnInit(): void {
    this.subscribeToServices();
    this.loadAllTodos();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private subscribeToServices(): void {
    // Subscribe to todos
    this.todoService.todos$
      .pipe(takeUntil(this.destroy$))
      .subscribe(todos => this.todos = todos);

    // Subscribe to stats
    this.todoService.stats$
      .pipe(takeUntil(this.destroy$))
      .subscribe(stats => this.stats = stats);

    // Subscribe to loading state
    this.loadingService.loading$
      .pipe(takeUntil(this.destroy$))
      .subscribe(loading => this.isLoading = loading);

    // Subscribe to errors
    this.errorHandlingService.error$
      .pipe(takeUntil(this.destroy$))
      .subscribe(error => this.errorMessage = error);
  }

  loadAllTodos(): void {
    this.todoService.loadTodos().subscribe();
  }

  loadCompletedTodos(): void {
    const filter: TodoFilter = { isCompleted: true };
    this.todoService.loadFilteredTodos(filter).subscribe();
  }

  loadPendingTodos(): void {
    const filter: TodoFilter = { isCompleted: false };
    this.todoService.loadFilteredTodos(filter).subscribe();
  }

  onSearchChange(): void {
    if (this.searchTerm.trim()) {
      const filter: TodoFilter = { searchTerm: this.searchTerm };
      this.todoService.loadFilteredTodos(filter).subscribe();
    } else {
      this.loadAllTodos();
    }
  }

  addTodo(): void {
    if (!this.newTodoTitle.trim()) return;

    const newTodo: CreateTodoItem = {
      title: this.newTodoTitle.trim(),
      description: this.newTodoDescription.trim() || undefined
    };

    this.todoService.createTodo(newTodo).subscribe(() => {
      this.newTodoTitle = '';
      this.newTodoDescription = '';
    });
  }

  toggleCompletion(id: string): void {
    this.todoService.toggleTodoCompletion(id).subscribe();
  }

  deleteTodo(id: string): void {
    if (confirm('Are you sure you want to delete this todo?')) {
      this.todoService.deleteTodo(id).subscribe();
    }
  }

  clearError(): void {
    this.errorHandlingService.clearError();
  }
}
