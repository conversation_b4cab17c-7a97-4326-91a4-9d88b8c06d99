import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';
import { AuthApplicationService } from '../../../application/services/auth-application.service';
import { LoadingService } from '../../../core/services/loading.service';
import { ErrorHandlingService } from '../../../core/services/error-handling.service';
import { LoginCredentials } from '../../../domain/models/user.model';

/**
 * Login component - Presentation layer
 * Handles user authentication
 */
@Component({
  selector: 'app-login',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="login-container">
      <div class="login-form">
        <h2>Login</h2>
        
        <!-- Error Display -->
        <div *ngIf="errorMessage" class="error-message">
          {{ errorMessage }}
          <button (click)="clearError()" class="close-btn">&times;</button>
        </div>

        <!-- Loading Indicator -->
        <div *ngIf="isLoading" class="loading">
          Logging in...
        </div>

        <form (ngSubmit)="onLogin()" #loginForm="ngForm">
          <div class="form-group">
            <label for="email">Email:</label>
            <input 
              type="email" 
              id="email"
              [(ngModel)]="credentials.email" 
              name="email"
              required 
              email
              class="form-control"
              placeholder="Enter your email">
          </div>

          <div class="form-group">
            <label for="password">Password:</label>
            <input 
              type="password" 
              id="password"
              [(ngModel)]="credentials.password" 
              name="password"
              required 
              class="form-control"
              placeholder="Enter your password">
          </div>

          <button 
            type="submit" 
            [disabled]="!loginForm.form.valid || isLoading"
            class="login-btn">
            {{ isLoading ? 'Logging in...' : 'Login' }}
          </button>
        </form>

        <div class="register-link">
          <p>Don't have an account? <a href="/register">Register here</a></p>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .login-container {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      background-color: #f5f5f5;
    }

    .login-form {
      background: white;
      padding: 40px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      width: 100%;
      max-width: 400px;
    }

    h2 {
      text-align: center;
      margin-bottom: 30px;
      color: #333;
    }

    .error-message {
      background-color: #f8d7da;
      color: #721c24;
      padding: 10px;
      border-radius: 4px;
      margin-bottom: 20px;
      position: relative;
    }

    .close-btn {
      position: absolute;
      right: 10px;
      top: 5px;
      background: none;
      border: none;
      font-size: 20px;
      cursor: pointer;
    }

    .loading {
      text-align: center;
      padding: 20px;
      font-style: italic;
      color: #666;
    }

    .form-group {
      margin-bottom: 20px;
    }

    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
      color: #333;
    }

    .form-control {
      width: 100%;
      padding: 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 16px;
      box-sizing: border-box;
    }

    .form-control:focus {
      outline: none;
      border-color: #007bff;
      box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
    }

    .login-btn {
      width: 100%;
      padding: 12px;
      background-color: #007bff;
      color: white;
      border: none;
      border-radius: 4px;
      font-size: 16px;
      cursor: pointer;
      transition: background-color 0.2s;
    }

    .login-btn:hover:not(:disabled) {
      background-color: #0056b3;
    }

    .login-btn:disabled {
      background-color: #ccc;
      cursor: not-allowed;
    }

    .register-link {
      text-align: center;
      margin-top: 20px;
    }

    .register-link a {
      color: #007bff;
      text-decoration: none;
    }

    .register-link a:hover {
      text-decoration: underline;
    }
  `]
})
export class LoginComponent implements OnInit, OnDestroy {
  credentials: LoginCredentials = {
    email: '',
    password: ''
  };

  isLoading = false;
  errorMessage: string | null = null;
  
  private destroy$ = new Subject<void>();

  constructor(
    private authService: AuthApplicationService,
    private loadingService: LoadingService,
    private errorHandlingService: ErrorHandlingService,
    private router: Router
  ) {}

  ngOnInit(): void {
    // Check if already authenticated
    if (this.authService.isAuthenticated()) {
      this.router.navigate(['/todos']);
      return;
    }

    this.subscribeToServices();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private subscribeToServices(): void {
    // Subscribe to loading state
    this.loadingService.loading$
      .pipe(takeUntil(this.destroy$))
      .subscribe(loading => this.isLoading = loading);

    // Subscribe to errors
    this.errorHandlingService.error$
      .pipe(takeUntil(this.destroy$))
      .subscribe(error => this.errorMessage = error);
  }

  onLogin(): void {
    if (!this.credentials.email || !this.credentials.password) {
      this.errorHandlingService.handleError('Please fill in all fields');
      return;
    }

    this.authService.login(this.credentials).subscribe({
      error: (error) => {
        console.error('Login failed:', error);
        // Error is handled by the interceptor and error handling service
      }
    });
  }

  clearError(): void {
    this.errorHandlingService.clearError();
  }
}
