import { Injectable } from '@angular/core';
import { Observable, map } from 'rxjs';
import { IAuthRepository } from '../../domain/repositories/auth.repository.interface';
import { AuthResponse, CreateUser, LoginCredentials } from '../../domain/models/user.model';
import { HttpClientService } from '../../core/services/http-client.service';

/**
 * Implementation of authentication repository
 * Handles HTTP communication for authentication operations
 */
@Injectable({
  providedIn: 'root'
})
export class AuthRepository implements IAuthRepository {
  
  constructor(private httpClient: HttpClientService) {}

  /**
   * Register a new user
   */
  register(userData: CreateUser): Observable<AuthResponse> {
    return this.httpClient.post<AuthResponse>('auth/register', userData)
      .pipe(
        map(response => ({
          ...response,
          user: {
            ...response.user,
            createdAt: new Date(response.user.createdAt),
            updatedAt: new Date(response.user.updatedAt)
          },
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours from now
        }))
      );
  }

  /**
   * Login with email and password
   */
  login(credentials: LoginCredentials): Observable<AuthResponse> {
    return this.httpClient.post<AuthResponse>('auth/login', credentials)
      .pipe(
        map(response => ({
          ...response,
          user: {
            ...response.user,
            createdAt: new Date(response.user.createdAt),
            updatedAt: new Date(response.user.updatedAt)
          },
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours from now
        }))
      );
  }

  /**
   * Logout the current user
   */
  logout(): Observable<void> {
    // For now, just return an observable that completes
    // In a real app, you might want to call a logout endpoint
    return new Observable(observer => {
      observer.next();
      observer.complete();
    });
  }

  /**
   * Refresh the authentication token
   */
  refreshToken(): Observable<AuthResponse> {
    return this.httpClient.post<AuthResponse>('auth/refresh', {});
  }

  /**
   * Verify if the current token is valid
   */
  verifyToken(): Observable<boolean> {
    return this.httpClient.get<{ valid: boolean }>('auth/verify')
      .pipe(
        map(response => response.valid)
      );
  }
}
