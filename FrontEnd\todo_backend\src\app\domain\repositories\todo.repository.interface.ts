import { Observable } from 'rxjs';
import { TodoItem, CreateTodoItem, UpdateTodoItem, TodoFilter } from '../models/todo.model';

/**
 * Repository interface for Todo data access
 * This defines the contract for todo data operations
 */
export interface ITodoRepository {
  /**
   * Get all todos for the authenticated user
   */
  getAllTodos(): Observable<TodoItem[]>;

  /**
   * Get filtered todos based on criteria
   */
  getFilteredTodos(filter: TodoFilter): Observable<TodoItem[]>;

  /**
   * Get a specific todo by ID
   */
  getTodoById(id: string): Observable<TodoItem>;

  /**
   * Create a new todo
   */
  createTodo(todo: CreateTodoItem): Observable<TodoItem>;

  /**
   * Update an existing todo
   */
  updateTodo(id: string, todo: UpdateTodoItem): Observable<TodoItem>;

  /**
   * Delete a todo
   */
  deleteTodo(id: string): Observable<void>;

  /**
   * Get todo statistics
   */
  getTodoStats(): Observable<{ totalCount: number; completedCount: number }>;
}
