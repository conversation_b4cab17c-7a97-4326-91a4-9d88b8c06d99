import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { ApiError } from '../../domain/models/api.model';

/**
 * Centralized error handling service
 */
@Injectable({
  providedIn: 'root'
})
export class ErrorHandlingService {
  private errorSubject = new BehaviorSubject<string | null>(null);
  public error$ = this.errorSubject.asObservable();

  /**
   * Handle and display error messages
   */
  handleError(message: string): void {
    console.error('Application Error:', message);
    this.errorSubject.next(message);
    
    // Auto-clear error after 5 seconds
    setTimeout(() => {
      this.clearError();
    }, 5000);
  }

  /**
   * Handle API errors
   */
  handleApiError(error: ApiError): void {
    const message = error.details || error.message || 'An unexpected error occurred';
    this.handleError(message);
  }

  /**
   * Clear current error
   */
  clearError(): void {
    this.errorSubject.next(null);
  }

  /**
   * Get current error
   */
  getCurrentError(): string | null {
    return this.errorSubject.value;
  }
}
